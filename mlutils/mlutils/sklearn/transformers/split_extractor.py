import numpy as np
from sklearn.base import BaseEstimator, TransformerMixin

from mlutils.sklearn.transformers.mixins import FeatureNameOverrideMixin


class SplitExtractor(BaseEstimator, TransformerMixin, FeatureNameOverrideMixin):
    def __init__(self, column, delimiter, position, feature_names_out=None):
        self.column = column
        self.delimiter = delimiter
        self.position = position
        self.feature_names_out = feature_names_out

    def fit(self, X, y=None):
        return self

    def transform(self, X):
        # Identify rows where the delimiter is missing
        missing_delimiter_mask = (
            X[self.column].isna()
            | (X[self.column] == "")
            | ~X[self.column].str.contains(self.delimiter, na=False)
        )
        # Split text into multiple columns
        split_series = X[self.column].str.split(self.delimiter, expand=True)

        # Use `.reindex()` to safely access the column or fill with NaN if missing
        out = split_series.reindex(columns=[self.position], fill_value=np.nan)

        # Apply NaN for rows where the delimiter was missing
        out.loc[missing_delimiter_mask] = np.nan

        return out
