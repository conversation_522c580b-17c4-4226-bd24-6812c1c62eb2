import pandas as pd
import numpy as np
from pandas.testing import assert_frame_equal

from mlutils.sklearn.transformers import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    HaversineDistanceCalculator,
    Lowercaser,
    Multiplier,
    SplitExtractor,
    StringLengthExtractor,
    Subtractor,
    TimezoneConverter,
    TypeSetter,
)


def test_contains_checker():
    """Test basic substring matching functionality"""
    data = pd.DataFrame(
        {
            "substring": ["apple", "banana", None, "grape", "r", "APPLE", "melon"],
            "text": [
                "I have an apple",
                "apple",
                "empty",
                None,
                "strawberry",
                "I have an apple",
                "WATERMELON",
            ],
        }
    )

    transformer = ContainsChecker(substring_col="substring", text_col="text")
    result = transformer.fit_transform(data)

    expected = pd.DataFrame(
        [
            True,  # "apple" in "I have an apple"
            False,  # "banana" in "I have an apple"
            np.nan,  # None in "empty"
            np.nan,  # "grape" in None
            True,  # "r" in "strawberry"
            True,  # "APPLE" in "I have an apple"
            True,  # "melon" in "WATERMELON"
        ],
        index=data.index,
        dtype="boolean",
    )

    assert_frame_equal(result, expected)


def test_date_differ():
    """Test date difference calculation with key scenarios"""
    data = pd.DataFrame(
        {
            "start_date": ["2023-01-01", "2023-01-15", None],
            "end_date": ["2023-01-03", None, "2023-04-01"],
        }
    )

    transformer = DateDiffer(
        subtrahend_col="start_date", minuend_col="end_date", timedelta_unit="D"
    )
    result = transformer.fit_transform(data)

    expected = pd.DataFrame(
        [
            2.0,  # Normal case: 2 days difference
            np.nan,  # Minuend is null
            np.nan,  # Subtrahend is null
        ]
    )

    pd.testing.assert_frame_equal(result, expected, check_names=False)


def test_equality_checker():
    """Test equality checking with key scenarios"""
    data = pd.DataFrame(
        {"col1": ["apple", "banana", None], "col2": ["apple", "orange", "grape"]}
    )

    transformer = EqualityChecker(col1="col1", col2="col2")
    result = transformer.fit_transform(data)

    expected = pd.DataFrame(
        [
            1.0,  # Normal case: "apple" == "apple"
            0.0,  # Normal case: "banana" != "orange"
            np.nan,  # Null handling
        ]
    )

    pd.testing.assert_frame_equal(result, expected, check_names=False)


def test_haversine_distance_calculator():
    """Test haversine distance calculation with key scenarios"""
    # San Francisco coordinates
    sf_lat, sf_lon = 37.7749, -122.4194
    # Los Angeles coordinates
    la_lat, la_lon = 34.0522, -118.2437
    # Known distance between SF and LA is approximately 559 km

    data = pd.DataFrame(
        {
            "point1_lat": [sf_lat, None],
            "point1_lon": [sf_lon, -122.4194],
            "point2_lat": [la_lat, 34.0522],
            "point2_lon": [la_lon, -118.2437],
        }
    )

    transformer = HaversineDistanceCalculator(
        lat_col_1="point1_lat",
        lon_col_1="point1_lon",
        lat_col_2="point2_lat",
        lon_col_2="point2_lon",
    )

    result = transformer.fit_transform(data)

    expected = pd.DataFrame(
        [
            559.0,  # SF to LA: ~559 km (standard case with distance)
            np.nan,  # Null coordinates case
        ]
    )

    # Use approx equals for floating point comparison
    pd.testing.assert_frame_equal(result.round(0), expected, check_names=False)


def test_lowercaser():
    transformer = Lowercaser("a")
    X = pd.DataFrame({"a": ["Hello", "WORLD"], "b": ["Foo", "Bar"]})
    X_transformed = transformer.fit_transform(X)
    assert X_transformed.equals(pd.DataFrame({"a": ["hello", "world"]}))


def test_multiplier():
    """Test multiplication of two columns with key scenarios"""
    data = pd.DataFrame(
        {
            "a": [2, 10, None, 7],
            "b": [3, -2, 4, None],
        }
    )

    transformer = Multiplier(col1="a", col2="b")
    result = transformer.fit_transform(data)

    expected = pd.DataFrame(
        [
            6.0,  # Normal case: 2 * 3 = 6
            -20.0,  # Negative number case: 10 * -2 = -20
            None,  # Null in first column
            None,  # Null in second column
        ]
    )

    pd.testing.assert_frame_equal(result, expected, check_names=False)


def test_split_extractor():
    """Test string splitting and extraction with key scenarios"""
    data = pd.DataFrame(
        {
            "text": [
                "apple,banana,orange",  # Normal case with 3 elements
                "red,green",  # Normal case with 2 elements
                "",  # Empty string
                None,  # Null value
                "single",  # No delimiter
            ]
        }
    )

    # Test extraction of first element
    first_transformer = SplitExtractor(column="text", delimiter=",", position=0)
    first_result = first_transformer.fit_transform(data)

    first_expected = pd.DataFrame(
        [
            "apple",  # First element from "apple,banana,orange"
            "red",  # First element from "red,green"
            np.nan,  # Empty string stays empty
            np.nan,  # Null remains null
            np.nan,  # Single element (no delimiter) returns the full string
        ]
    )

    pd.testing.assert_frame_equal(first_result, first_expected, check_names=False)

    # Test extraction of second element
    second_transformer = SplitExtractor(column="text", delimiter=",", position=1)
    second_result = second_transformer.fit_transform(data)

    second_expected = pd.DataFrame(
        {
            1: [
                "banana",  # Second element from "apple,banana,orange"
                "green",  # Second element from "red,green"
                None,  # Empty string has no second element
                None,  # Null remains null
                None,  # Single element has no second element
            ]
        }
    )

    pd.testing.assert_frame_equal(second_result, second_expected, check_names=False)

    # Test extraction of an index beyond available elements
    third_transformer = SplitExtractor(
        column="text",
        delimiter=",",
        position=5,  # Position that doesn"t exist in any string
    )
    third_result = third_transformer.fit_transform(data)

    # All should be None as no string has 6 elements
    third_expected = pd.DataFrame(
        {
            5: [
                np.nan,
                np.nan,
                np.nan,
                np.nan,
                np.nan,
            ]
        }
    )

    pd.testing.assert_frame_equal(third_result, third_expected, check_names=False)

    # Test with a delimiter that doesn"t exist
    delimiter_transformer = SplitExtractor(
        column="text",
        delimiter="@",  # Delimiter not present in any string
        position=0,
    )
    delimiter_result = delimiter_transformer.fit_transform(data)
    # Should return the original strings as position 0
    delimiter_expected = pd.DataFrame(
        {
            0: [
                np.nan,
                np.nan,
                np.nan,
                np.nan,
                np.nan,
            ]
        },
        dtype="object",
    )

    pd.testing.assert_frame_equal(
        delimiter_result, delimiter_expected, check_names=False
    )


def test_string_length_extractor():
    """Test string length extraction with key scenarios"""
    data = pd.DataFrame(
        {
            "text": [
                "apple",  # 5 characters
                "banana split",  # 12 characters
                "",  # 0 characters (empty string)
                None,  # Null
            ]
        }
    )

    transformer = StringLengthExtractor(column="text")
    result = transformer.fit_transform(data)

    expected = pd.DataFrame(
        {
            "text": [
                5.0,  # Length of "apple"
                12.0,  # Length of "banana split"
                0.0,  # Length of empty string
                None,  # Null input gives null output
            ]
        }
    )

    pd.testing.assert_frame_equal(result, expected, check_names=False)


def test_subtractor():
    """Test multiplication of two columns with key scenarios"""
    data = pd.DataFrame({"a": [2, 5, None, 7], "b": [3, 0, 4, None]})

    transformer = Subtractor(minuend_col="a", subtrahend_col="b")
    result = transformer.fit_transform(data)

    expected = pd.DataFrame(
        [
            -1.0,  # Normal case: 2 - 3 = -1
            5.0,  # Multiplication by zero: 5 * 0 = 0
            None,  # Null in first column
            None,  # Null in second column
        ]
    )

    pd.testing.assert_frame_equal(result, expected, check_names=False)


def test_timezone_converter():
    """Test timezone conversion with key scenarios"""
    # Create test data
    data = pd.DataFrame(
        {
            "timestamp": pd.to_datetime(
                [
                    "2023-01-15 12:00:00",  # Noon in source timezone
                    None,  # Null value
                ]
            )
        }
    )

    # Test conversion from UTC to US Eastern
    utc_to_eastern = TimezoneConverter(
        datetime_col="timestamp", current_timezone="UTC", target_timezone="US/Eastern"
    )

    utc_to_eastern_result = utc_to_eastern.fit_transform(data)

    # UTC noon -> 7am Eastern (UTC-5)
    # UTC midnight -> 7pm Eastern (UTC-5)
    expected_utc_eastern = pd.DataFrame(
        {
            "timestamp": pd.to_datetime(["2023-01-15 07:00:00"]).tz_localize(
                "US/Eastern"
            )  # Ensure it"s explicitly US/Eastern
        }
    )

    # Ensure NaT is also tz-aware
    expected_utc_eastern.loc[1] = pd.NaT
    expected_utc_eastern["timestamp"] = expected_utc_eastern["timestamp"].astype(
        "datetime64[ns, US/Eastern]"
    )

    pd.testing.assert_frame_equal(
        utc_to_eastern_result, expected_utc_eastern, check_names=False
    )


def test_type_setter():
    """Test type conversion with key scenarios"""
    # Create test data with mixed types
    data = pd.DataFrame(
        {
            "mixed_column": ["1", "2", None, "4", "5"],  # Strings
            "numeric_column": [1, 2, 3, None, 5],  # Integers with None
            "bool_column": [True, False, True, None, False],  # Booleans with None
        }
    )

    # Test converting strings to integers
    int_transformer = TypeSetter(dtype="Int64")
    int_result = int_transformer.transform(data["mixed_column"].to_frame())
    expected_int = pd.DataFrame(
        {"mixed_column": pd.Series([1, 2, None, 4, 5], dtype="Int64")}
    )

    pd.testing.assert_frame_equal(int_result, expected_int, check_names=True)

    # Test converting to float
    float_transformer = TypeSetter(dtype="float")
    float_result = float_transformer.transform(data["numeric_column"].to_frame())
    expected_float = pd.DataFrame({"numeric_column": [1.0, 2.0, 3.0, None, 5.0]})

    pd.testing.assert_frame_equal(float_result, expected_float, check_names=True)

    # Test converting to string
    str_transformer = TypeSetter(dtype="str")
    str_result = str_transformer.transform(data["numeric_column"].to_frame())
    expected_str = pd.DataFrame({"numeric_column": ["1.0", "2.0", "3.0", "nan", "5.0"]})

    pd.testing.assert_frame_equal(str_result, expected_str, check_names=True)
