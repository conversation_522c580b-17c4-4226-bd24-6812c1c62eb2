[build-system]
build-backend = "hatchling.build"
requires = ["hatchling"]

[project]
authors = [
  {name = "Gametime ML"}
]
dependencies = []
description = "ML Utility Library"
name = "mlutils"
requires-python = ">=3.10,<4.0"
version = "1.9.0"

[project.optional-dependencies]
aiohttp = [
  "aiohttp>=3.8.5"
]
dev = [
  "pytest-asyncio>=0.25.3",
  "pytest>=8.3.4"
]
grpc = [
  "grpcio-health-checking>=1.71.0,<2.0.0",
  "grpcio>=1.71.0,<2.0.0"
]
pandas = [
  "pandas>=2.0.0"
]
sklearn = [
  "numpy>=2.0.0",
  "pandas>=2.0.0",
  "scikit-learn>=1.5.0"
]

[tool.semantic_release]
build_command = ""
changelog_file = "CHANGELOG.md"
commit_parser = "conventional"
tag_format = "mlutils-v{version}"
upload_to_release = true
upload_to_repository = false
version_toml = ["pyproject.toml:project.version"]

[tool.semantic_release.branches]
main.match = "main"
