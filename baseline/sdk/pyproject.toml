[build-system]
build-backend = "hatchling.build"
requires = ["hatchling"]

[project]
authors = [
  {name = "Gametime ML"}
]
dependencies = [
  "PyGithub>=1.55",
  "boto3>=1.23.0",
  "gametime_protos>1.0.0",
  "joblib>=1.1.0",
  "mlutils[grpc]>=1.0.0",
  "papermill>=2.3.4"
]
description = "Baseline sdk"
name = "baseline-sdk"
requires-python = ">=3.10,<4.0"
version = "1.6.0"

[project.entry-points."papermill.engine"]
baseline_engine = "baseline.papermill.engines:BaselineEngine"

[project.entry-points."papermill.io"]
"https://github.com/" = "baseline.papermill.handlers:BaselineGithubHandler"

[project.optional-dependencies]
dev = [
  "ipykernel>=6.29.5",
  "ipython>=8.34.0",
  "numpy>=1.21.0",
  "pandas>=1.2.0",
  "pytest-httpserver",
  "pytest>=7.0.0",
  "scikit-learn>=1.0.0",
  "testcontainers[minio]>=4.9.2",
  "torch>=2.6.0"
]

[tool.hatch.build.targets.wheel]
packages = ["baseline"]

[tool.hatch.metadata]
allow-direct-references = true

[tool.semantic_release]
build_command = ""
changelog_file = "CHANGELOG.md"
commit_parser = "conventional"
tag_format = "baseline-sdk-v{version}"
upload_to_release = true
upload_to_repository = false
version_toml = ["pyproject.toml:project.version"]

[tool.semantic_release.branches]
main.match = "main"

[[tool.uv.index]]
name = "codeartifact"
url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/"

[tool.uv.sources]
gametime_protos = {index = "codeartifact"}
mlutils = {index = "codeartifact"}
